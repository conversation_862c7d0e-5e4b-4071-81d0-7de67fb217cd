package com.qzing.bsp.osc.controller;

import com.qzing.bsp.osc.entity.OSCTask;
import com.qzing.bsp.osc.entity.OSCTaskProcessRecord;
import com.qzing.bsp.osc.service.OSCTaskService;
import com.qzing.ieep.coding.template.controller.ControllerTemplate;
import com.qzing.ieep.data.common.RestResponse;
import com.qzing.ieep.data.jpa.support.JpaSearchRequest;
import com.qzing.ieep.license.annotation.Certificate;
import com.qzing.ieep.license.annotation.Certificate.RequiredType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Api(tags = "任务管理中间层接口")
@Certificate(value = { "OSC" }, requiredType = RequiredType.ONE)
public class StdOSCTaskController extends ControllerTemplate<OSCTask, Long> {

    @Autowired
    protected OSCTaskService service;

    @ApiOperation(value = "详情")
    @PostMapping("/get")
    public RestResponse get(@RequestBody OSCTask request) {
        OSCTask model = service.get(request);
        return RestResponse.success(model);
    }

    @ApiOperation(value = "执行记录列表")
    @PostMapping("/findprocessrecordlist")
    public RestResponse findProcessRecordList(@RequestBody JpaSearchRequest request) {
        List<OSCTaskProcessRecord> recordList = service.findProcessRecordList(request);
        return RestResponse.success(recordList);
    }

    @ApiOperation(value = "派单")
    @PostMapping("/dispatch")
    public RestResponse dispatch(@RequestBody OSCTask request) {
        return service.dispatch(request);
    }

    @ApiOperation(value = "任务完成")
    @PostMapping("/finish")
    public RestResponse finish(@RequestBody OSCTask request) {
        return service.finish(request);
    }

    @ApiOperation(value = "关闭")
    @PostMapping("/close")
    public RestResponse close(@RequestBody OSCTask request) {
        return service.close(request);
    }

    @ApiOperation(value = "激活")
    @PostMapping("/activation")
    public RestResponse activation(@RequestBody OSCTask request) {
        return service.activation(request);
    }

    @ApiOperation(value = "重新派单")
    @PostMapping("/redispatch")
    public RestResponse reDispatch(@RequestBody OSCTask request) {
        return service.reDispatch(request);
    }

    @ApiOperation(value = "催办")
    @PostMapping("/urging")
    public RestResponse urging(@RequestBody OSCTask request) {
        return service.urging(request);
    }

    @ApiOperation(value = "任务预警")
    @PostMapping("/warning")
    public RestResponse warning(@RequestBody OSCTask request) {
        return service.warning(request);
    }

    @ApiOperation(value = "挂起")
    @PostMapping("/pending")
    public RestResponse pending(@RequestBody OSCTask request) {
        return service.pending(request);
    }

}
