package com.qzing.bsp.osc.entity;

import com.qzing.ieep.data.annotation.BizNumber;
import com.qzing.ieep.data.common.FieldMetadata;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description [风险研判]
 * @date 2025/7/25
*/
@Data
@MappedSuperclass
public class StdOSCRiskJudge extends OSCEntity<Long> {


    private static final long serialVersionUID = 5180125584416159587L;

    @Id
    @GenericGenerator(strategy = "com.qzing.ieep.data.jpa.support.SnowFlakeIdGenerator", name = "SnowFlakeIdGenerator")
    @GeneratedValue(generator = "SnowFlakeIdGenerator")
    @Column(name = "riskJudgeId")
    @FieldMetadata(name = "主键ID", resourceKey = "label.taskId", skipHistoryComparison = true)
    protected Long riskJudgeId;

    @BizNumber
    @Column(name = "riskJudgeNo")
    @FieldMetadata(name = "风险研判单号", resourceKey = "OSCRISK.label.riskJudgeNo")
    protected String riskJudgeNo;

    @Column(name = "judgeStatus")
    @FieldMetadata(name = "单据状态", resourceKey = "OSCRISK.label.judgeStatus")
    protected String judgeStatus;

    @Column(name = "unitCode")
    @FieldMetadata(name = "单位编码", resourceKey = "OSCRISK.label.unit")
    protected String unitCode;

    @Column(name = "unitName")
    @FieldMetadata(name = "单位名称", resourceKey = "OSCRISK.label.unit")
    protected String unitName;

    @Column(name = "riskLog")
    @FieldMetadata(name = "风险登记记录", resourceKey = "OSCRISK.label.riskLog")
    protected String riskLog;

    @Column(name = "riskSource")
    @FieldMetadata(name = "风险要素来源", resourceKey = "OSCRISK.label.riskSource")
    protected String riskSource;

    @Column(name = "sceneCode")
    @FieldMetadata(name = "场景编码", resourceKey = "OSCRISK.label.sceneCode")
    protected String sceneCode;

    @Column(name = "sceneName")
    @FieldMetadata(name = "场景名称", resourceKey = "OSCRISK.label.sceneName")
    protected String sceneName;

    @Column(name = "bizType")
    @FieldMetadata(name = "业务类型", resourceKey = "OSCRISK.label.bizType")
    protected String bizType;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "riskJudge", fetch = FetchType.LAZY, orphanRemoval = true)
    @FieldMetadata(resourceKey = "label.taskDtls")
    protected List<OSCRiskJudgeDtl> riskJudgeDtls = new ArrayList<OSCRiskJudgeDtl>();

}
