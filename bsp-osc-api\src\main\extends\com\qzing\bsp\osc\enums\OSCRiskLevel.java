package com.qzing.bsp.osc.enums;

/**
 * 风险等级
 */
public enum OSCRiskLevel {

    HIGH("高"),
    MEDIUM("中"),
    LOW("低"),
    NONE("无");

    private String desc;

    private OSCRiskLevel(String desc) {
        this.desc = desc;
    }

    public String desc() {
        return this.desc;
    }

    public Integer value() {
        return this.ordinal();
    }

    public String toString() {
        return String.valueOf(this.ordinal());
    }

    public int getIndex() {
        return this.ordinal();
    }

    public String getName() {
        return this.name();
    }

    public String getStateDesc() {
        return this.desc();
    }
}
