package com.qzing.bsp.osc.enums;

/**
 * 任务类型
 */
public enum OSCTaskType {

    RISK("风险任务"),
    DAILY("日常任务"),
    SPECIAL("专项任务");

    private String desc;

    private OSCTaskType(String desc) {
        this.desc = desc;
    }

    public String desc() {
        return this.desc;
    }

    public Integer value() {
        return this.ordinal();
    }

    public String toString() {
        return String.valueOf(this.ordinal());
    }

    public int getIndex() {
        return this.ordinal();
    }

    public String getName() {
        return this.name();
    }

    public String getStateDesc() {
        return this.desc();
    }

}
