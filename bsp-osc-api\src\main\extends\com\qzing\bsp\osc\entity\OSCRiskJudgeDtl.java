package com.qzing.bsp.osc.entity;

import com.qzing.ieep.data.common.DataPermission;
import com.qzing.ieep.datadriven.DataDrivenEntityListener;
import com.qzing.ieep.logging.listener.AuditListener;

import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;

/**
 * 风险研判管理-明细
 */
@Entity
@Table(name = "d_osc_riskjudgedtl")
@EntityListeners({AuditListener.class, DataDrivenEntityListener.class})
@DataPermission(billTypeCode = "RJS")
public class OSCRiskJudgeDtl extends StdOSCRiskJudgeDtl {

	private static final long serialVersionUID = -3449422604257537084L;

}
