package com.qzing.bsp.osc.controller;

import com.qzing.bsp.osc.entity.RiskSuspicionRecord;
import com.qzing.bsp.osc.service.RiskSuspicionRecordService;
import com.qzing.ieep.coding.template.controller.SimpleControllerTemplate;
import com.qzing.ieep.license.annotation.Certificate;
import com.qzing.ieep.license.annotation.Certificate.RequiredType;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;

@Api(tags = "风险疑点记录中间层接口")
@Certificate(value = {"FXYD"}, requiredType = RequiredType.ONE)
public class StdRiskSuspicionRecordController extends SimpleControllerTemplate<RiskSuspicionRecord, Long> {

    @Autowired
    protected RiskSuspicionRecordService service;

}
