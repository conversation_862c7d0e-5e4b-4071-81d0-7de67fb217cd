package com.qzing.bsp.osc.entity;

import com.qzing.bsp.osc.enums.OSCState;
import com.qzing.ieep.data.common.FieldMetadata;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Calendar;

/**
 * 任务完成过程记录-基类
 */
@Data
@MappedSuperclass
public class StdOSCTaskProcessRecord extends OSCEntity<Long> {

	private static final long serialVersionUID = 2526335682158437861L;

	@Id
	@GenericGenerator(strategy = "com.qzing.ieep.data.jpa.support.SnowFlakeIdGenerator", name = "SnowFlakeIdGenerator")
	@GeneratedValue(generator = "SnowFlakeIdGenerator")
	@Column(name = "recordId")
	@FieldMetadata(name = "任务执行记录ID", resourceKey = "label.recordId", skipHistoryComparison = true)
	protected Long recordId;

	@Column(name = "taskNo")
	@FieldMetadata(name = "任务单号", resourceKey = "label.taskNo")
	protected String taskNo;

	@Column(name = "taskId")
	@FieldMetadata(name = "任务单ID", resourceKey = "label.taskId")
	protected Long taskId;

	@Column(name = "processAction")
	@FieldMetadata(name = "动作", resourceKey = "label.action")
	protected OSCState processAction;

	@Column(name = "operatorCode")
	@FieldMetadata(name = "操作人编码", resourceKey = "label.operatorCode")
	protected String operatorCode;

	@Column(name = "operatorName")
	@FieldMetadata(name = "操作人名称", resourceKey = "label.operatorName")
	protected String operatorName;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "operatorTime")
	@FieldMetadata(name = "操作时间", resourceKey = "label.operatorTime")
	protected Calendar operatorTime;

}
