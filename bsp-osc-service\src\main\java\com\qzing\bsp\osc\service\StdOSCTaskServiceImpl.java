package com.qzing.bsp.osc.service;

import com.qzing.bpm.support.annotation.BpmService;
import com.qzing.bsp.osc.annotation.ModuleInfo;
import com.qzing.bsp.osc.constant.OSCBillType;
import com.qzing.bsp.osc.dao.OSCTaskProcessRecordDao;
import com.qzing.bsp.osc.entity.OSCTask;
import com.qzing.bsp.osc.entity.OSCTaskProcessRecord;
import com.qzing.bsp.osc.enums.OSCState;
import com.qzing.core.sys.api.BillSetServiceClient;
import com.qzing.ieep.coding.template.service.ServiceImplTemplate;
import com.qzing.ieep.context.CurrentContextHelper;
import com.qzing.ieep.core.exception.BizException;
import com.qzing.ieep.data.common.RestResponse;
import com.qzing.ieep.data.jpa.support.JpaSearchRequest;
import com.qzing.ieep.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.UUID;


/**
 * 任务管理
 */
@BpmService(billTypeCode = OSCBillType.TASK, billNoKey = "taskNo")
@ModuleInfo(moduleCode = OSCBillType.TASK)
public class StdOSCTaskServiceImpl extends ServiceImplTemplate<OSCTask, Long> implements StdOSCTaskService {

    @Autowired(required = false)
    protected BillSetServiceClient billSetServiceClient;
    @Autowired
    protected OSCTaskProcessRecordDao taskProcessRecordDao;

    /**
     * 获取详情
     * @param request
     * @return
     */
    public OSCTask get(OSCTask request) {
        if(request == null || request.getTaskId() == null) {
            BizException.throwEx(getText("common.valid.request.noBlank"));
        }
        OSCTask task = findById(request.getTaskId());
        if(task == null) {
            BizException.throwEx(getText("common.valid.dataNotExist"));
        }
        return task;
    }

    /**
     * 执行记录列表
     * @param request
     * @return
     */
    public List<OSCTaskProcessRecord> findProcessRecordList(JpaSearchRequest request) {
        if(request == null || request.getParams() == null || request.getParams().isEmpty()) {
            BizException.throwEx(getText("common.valid.request.noBlank"));
        }
        return taskProcessRecordDao.findAll(request.getParams());
    }

    /**
     * 保存前置事件
     * @param entity
     * @param extraMap
     * @return
     */
    @Override
    public void beforeSave(OSCTask entity, Map<String, Object> extraMap) {
        CurrentContextHelper.get().getHttpHeaders().put("moduleCode", OSCBillType.TASK);
        //设置状态为待派单
        entity.setState(OSCState.WAITDISPATCH);
        //TODO 任务单号
//        String taskNo = billSetServiceClient.createNextRunningNum(OSCBillType.TASK);
        String taskNo = UUID.randomUUID().toString().replace("-", "");
        entity.setTaskNo(taskNo);
    }

    /**
     * 数据校验
     * @param entity
     * @param extraMap
     */
    @Override
    public void validate(OSCTask entity, Map<String, Object> extraMap) {

    }

    /**
     * 派单	指派任务负责人，并且发送消息通知
     * @param request
     * @return
     */
    @Override
    public RestResponse dispatch(OSCTask request) {
        if (request == null || request.getTaskId() == null) {
            return RestResponse.error("参数不完整");
        }
        if (StringUtils.isBlank(request.getTaskOwnerCode()) || StringUtils.isBlank(request.getTaskOwnerName())) {
            return RestResponse.error("责任人编码和名称不能为空");
        }
        OSCTask task = findById(request.getTaskId());
        if (task == null) {
            return RestResponse.error("任务不存在");
        }
        if (!OSCState.WAITDISPATCH.equals(task.getState())) {
            return RestResponse.error("只有待派单状态的任务才能派单");
        }
        task.setTaskOwnerCode(request.getTaskOwnerCode());
        task.setTaskOwnerName(request.getTaskOwnerName());
        task.setDispatchTime(Calendar.getInstance());
        task.setState(OSCState.WAITEXECUTE);
        save(task);

        // 发送通知
//        if (messageServiceClient != null) {
//            messageServiceClient.sendMessage(request.getAssignee(), "任务派单通知",
//                    "您有一个新任务：" + task.getTaskName());
//        }

        return RestResponse.success("派单成功");
    }

    /**
     * 任务完成	任务负责人支持提交完成任务，上传相关附件，通知创建人
     * @return
     */
    @Override
    public RestResponse finish(OSCTask request) {
        if (request == null || request.getTaskId() == null) {
            return RestResponse.error("参数不完整");
        }
        OSCTask task = findById(request.getTaskId());
        if (task == null) {
            return RestResponse.error("任务不存在");
        }
        if (!OSCState.WAITEXECUTE.equals(task.getState())) {
            return RestResponse.error("只有待执行的任务才能完成");
        }
        //实际完成时间
        task.setActualFinishTime(Calendar.getInstance());
        task.setState(OSCState.COMPLETED);
        //完成提交附件
        Long fileGroup = bindFileGroup(request.getFinishFileGroupId(), request.getFinishFileInfoList());
        task.setFinishFileGroupId(fileGroup);
        //完成说明
        task.setCompletionDesc(request.getCompletionDesc());
        save(task);

        // 添加处理记录
        addProcessRecord(task, OSCState.COMPLETED, request.getRemark());

        // 通知创建人
//        if (messageServiceClient != null && task.getCreator() != null) {
//            messageServiceClient.sendMessage(task.getCreator(), "任务完成通知",
//                    "任务[" + task.getTaskName() + "]已完成，请验收");
//        }
        return RestResponse.success("任务完成提交成功，等待验收");
    }

    /**
     * 关闭	创建人进行验收，没有问题，关闭该任务
     * @return
     */
    @Override
    public RestResponse close(OSCTask request) {
        if (request == null || request.getTaskId() == null) {
            return RestResponse.error("任务ID不能为空");
        }

        OSCTask task = findById(request.getTaskId());
        if (task == null) {
            return RestResponse.error("任务不存在");
        }

        if (!OSCState.COMPLETED.equals(task.getState())) {
            return RestResponse.error("只有完成的任务才能关闭");
        }

        task.setState(OSCState.CLOSED);
        task.setCloseTime(Calendar.getInstance());
        save(task);

        // 通知负责人
//        if (messageServiceClient != null && task.getAssignee() != null) {
//            messageServiceClient.sendMessage(task.getAssignee(), "任务关闭通知",
//                    "任务[" + task.getTaskName() + "]已关闭");
//        }

        return RestResponse.success("任务关闭成功");
    }

    /**
     * 激活	创建人进行验收，有疑问激活任务
     * @param request
     * @return
     */
    @Override
    public RestResponse activation(OSCTask request) {
        // 参数校验
        if (request == null || request.getTaskId() == null) {
            return RestResponse.error("任务ID不能为空");
        }

        // 获取任务详情
        OSCTask task = findById(request.getTaskId());
        if (task == null) {
            return RestResponse.error("任务不存在或已被删除");
        }

        // 状态校验（只有挂起状态可以激活）
        if (!OSCState.SUSPENDED.equals(task.getState())) {
            return RestResponse.error("只有挂起状态的任务才能激活");
        }

        // 校验操作权限（创建人或管理员）
        Long currentUserId = CurrentContextHelper.getUserId();
//        if (!currentUserId.equals(task.getCreateUserId())) {
//            return RestResponse.fail("只有任务创建人或管理员可以激活任务");
//        }

        // 更新任务状态
        task.setState(OSCState.WAITEXECUTE);
        // 清除挂起时间
        task.setSuspendedTime(null);
        //激活原因
        task.setActivationReason(request.getActivationReason());
        //记录激活次数
        Integer activationCount =  task.getActivationCount() == null?0: task.getActivationCount();
        task.setActivationCount(activationCount+1);
        save(task);

        // 记录激活操作
        addProcessRecord(task, OSCState.ACTIVATED, request.getActivationReason());
        // 发送通知
//        if (messageServiceClient != null && task.getAssignee() != null) {
//            String content = String.format(
//                    "任务【%s】已被激活，请继续处理。原因：%s",
//                    task.getTaskName(),
//                    StringUtils.defaultIfBlank(request.getRemark(), "无")
//            );
//
//            messageServiceClient.sendMessage(
//                    task.getAssignee(),
//                    "任务激活通知",
//                    content,
//                    Map.of("taskId", task.getTaskId())
//            );
//        }

        return RestResponse.success("任务激活成功");
    }

    /**
     * 重新派单	因为任务负责人无法执行，支持风控人重新派单
     * @param request
     * @return
     */
    @Override
    public RestResponse reDispatch(OSCTask request) {
        // 参数校验
        if (request == null || request.getTaskId() == null) {
            return RestResponse.error("任务ID不能为空");
        }

        if (StringUtils.isBlank(request.getTaskOwnerCode()) || StringUtils.isBlank(request.getTaskOwnerName())) {
            return RestResponse.error("责任人编码和名称不能为空");
        }

        // 获取任务详情
        OSCTask task = findById(request.getTaskId());
        if (task == null) {
            return RestResponse.error("任务不存在或已被删除");
        }

        // 状态校验
        if (!OSCState.WAITEXECUTE.equals(task.getState())) {
            return RestResponse.error("只有待执行的任务才能重新派单");
        }

        // 校验新负责人是否变化
        if (request.getTaskOwnerCode().equals(task.getTaskOwnerCode())) {
            return RestResponse.error("新负责人与当前负责人相同");
        }

        // 记录原负责人
        String oldOwnerCode = task.getTaskOwnerCode();

        // 更新任务信息
        task.setTaskOwnerCode(request.getTaskOwnerCode());
        task.setTaskOwnerName(request.getTaskOwnerName());
        task.setDispatchTime(Calendar.getInstance());
        save(task);

        // 发送通知
//        if (messageServiceClient != null) {
//            // 给新负责人发送通知
//            messageServiceClient.sendMessage(
//                    request.getAssignee(),
//                    "新任务分配通知",
//                    String.format("您已被分配任务【%s】，请及时处理", task.getTaskName()),
//                    Map.of("taskId", task.getTaskId())
//            );
//
//            // 给原负责人发送通知（如果存在）
//            if (StringUtils.isNotBlank(oldAssignee)) {
//                messageServiceClient.sendMessage(
//                        oldAssignee,
//                        "任务重新分配通知",
//                        String.format("您负责的任务【%s】已重新分配", task.getTaskName()),
//                        Map.of("taskId", task.getTaskId())
//                );
//            }
//        }

        return RestResponse.success("重新派单成功");
    }

    /**
     * 催办	支持风控人对任务负责人进行催办提醒
     * @param request
     * @return
     */
    @Override
    public RestResponse urging(OSCTask request) {
        // 参数校验
        if (request == null || request.getTaskId() == null) {
            return RestResponse.error("任务ID不能为空");
        }
        // 获取任务详情
        OSCTask task = findById(request.getTaskId());
        if (task == null) {
            return RestResponse.error("任务不存在或已被删除");
        }
        // 状态校验
        if (!OSCState.WAITEXECUTE.equals(task.getState())) {
            return RestResponse.error("只有待执行的任务才能催办");
        }

        // 校验当前用户权限（只有创建人或管理员可以催办）
//        String currentUserId = CurrentContextHelper.getCurrentUser().getUserId();
//        if (!currentUserId.equals(task.getCreator()) &&
//                !CurrentContextHelper.isAdmin()) {
//            return RestResponse.fail("只有任务创建人或管理员可以催办");
//        }

        // 构建催办内容
        String remark = StringUtils.isBlank(request.getRemark()) ?
                "请尽快处理任务" : request.getRemark();

        // 发送催办通知（给任务负责人）
//        if (messageServiceClient != null && task.getAssignee() != null) {
//            String content = String.format(
//                    "任务【%s】已被催办，催办原因：%s。当前状态：%s，计划完成时间：%s",
//                    task.getTaskName(),
//                    remark,
//                    task.getState().getDesc(),
//                    DateFormatUtils.format(task.getPlanEndTime(), "yyyy-MM-dd HH:mm")
//            );
//
//            messageServiceClient.sendMessage(
//                    task.getAssignee(),
//                    "任务催办通知",
//                    content,
//                    // 附加任务详情链接
//                    Map.of("taskId", task.getTaskId())
//            );
//        }
        return RestResponse.success("催办成功");
    }

    /**
     * 任务预警	任务快到期，还没有完成，支持提前预警
     * @param request
     * @return
     */
    @Override
    public RestResponse warning(OSCTask request) {
        if (request == null || request.getTaskId() == null) {
            return RestResponse.error("任务ID不能为空");
        }

        OSCTask task = findById(request.getTaskId());
        if (task == null) {
            return RestResponse.error("任务不存在");
        }

        if (!OSCState.WAITEXECUTE.equals(task.getState())) {
            return RestResponse.error("只有待执行的任务才能预警");
        }

        // 检查是否接近截止日期
        Calendar now = Calendar.getInstance();
        if (task.getRequireFinishDate().after(now)) {
//            long diff = task.getRequireFinishDate().getTime().getTime() - now.getTime();
//            long days = diff / (1000 * 60 * 60 * 24);
//
//            if (days <= 1) { // 1天内到期
//                // 添加处理记录
//                addProcessRecord(task, "任务预警", "任务即将到期，剩余时间不足1天");
//
//                // 发送预警通知
//                if (messageServiceClient != null && task.getAssignee() != null) {
//                    messageServiceClient.sendMessage(task.getAssignee(), "任务到期预警",
//                            "任务[" + task.getTaskName() + "]即将到期，请尽快处理");
//                }
//            }
        }

        return RestResponse.success("预警检查完成");
    }

    /**
     * 挂起	支持任务挂起，后续在激活执行
     * @param request
     * @return
     */
    @Override
    public RestResponse pending(OSCTask request) {
        if (request == null || request.getTaskId() == null) {
            return RestResponse.error("任务ID不能为空");
        }

        OSCTask task = findById(request.getTaskId());
        if (task == null) {
            return RestResponse.error("任务不存在");
        }

//        if (!OSCState.WAITEXECUTE.equals(task.getState())) {
//            return RestResponse.error("只有待执行的任务才能挂起");
//        }

        task.setState(OSCState.SUSPENDED);
        //挂起时间
        task.setSuspendedTime(Calendar.getInstance());
        save(task);

        // 通知相关人员
//        if (messageServiceClient != null) {
//            if (task.getCreator() != null) {
//                messageServiceClient.sendMessage(task.getCreator(), "任务挂起通知",
//                        "任务[" + task.getTaskName() + "]已挂起");
//            }
//            if (task.getAssignee() != null) {
//                messageServiceClient.sendMessage(task.getAssignee(), "任务挂起通知",
//                        "任务[" + task.getTaskName() + "]已挂起");
//            }
//        }

        return RestResponse.success("任务挂起成功");
    }

    /**
     * 添加任务处理记录
     * @param task 任务
     * @param action 操作
     * @param remark 备注
     */
    private void addProcessRecord(OSCTask task, OSCState action, String remark) {
        OSCTaskProcessRecord record = new OSCTaskProcessRecord();
        record.setTaskId(task.getTaskId());
        record.setProcessAction(action);
        record.setRemark(remark);
        record.setOperatorCode(CurrentContextHelper.getUserCode());
        record.setOperatorName(CurrentContextHelper.getUserName());
        record.setOperatorTime(Calendar.getInstance());
        taskProcessRecordDao.save(record);
    }

    //    编辑	当任务无法按时完成时，也支持调整任务计划

//    取消	任务因为种种原因无法执行，支持进行取消

    //删除	还未分派的任务支持手工删除

}
