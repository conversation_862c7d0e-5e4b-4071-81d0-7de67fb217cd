package com.qzing.bsp.osc.entity;

import com.qzing.ieep.data.common.DataPermission;
import com.qzing.ieep.datadriven.DataDrivenEntityListener;
import com.qzing.ieep.logging.listener.AuditListener;

import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;

/**
 * 任务管理实体类
 */
@Entity
@Table(name = "d_osc_task")
@EntityListeners({AuditListener.class, DataDrivenEntityListener.class})
@DataPermission(billTypeCode = "TASK")
public class OSCTask extends StdOSCTask {

	/**
	 * 序列化ID，用于对象序列化时的版本控制
	 */
	private static final long serialVersionUID = 2526335682158437861L;

}
