package com.qzing.bsp.osc.enums;

/**
 * OSC系统状态
 */
public enum OSCState {

    NEW("新建"),
    WAITDISPATCH("待派单"),
    WAITEXECUTE("待执行"),
    DISPATCHED("已派单"),
    COMPLETED("已完成"),
    CLOSED("已关闭"),
    ACTIVATED("激活"),
    SUSPENDED("挂起"),
    CANCEL("取消");

    private String desc;

    private OSCState(String desc) {
        this.desc = desc;
    }

    public String desc() {
        return this.desc;
    }

    public Integer value() {
        return this.ordinal();
    }

    public String toString() {
        return String.valueOf(this.ordinal());
    }

    public int getIndex() {
        return this.ordinal();
    }

    public String getName() {
        return this.name();
    }

    public String getStateDesc() {
        return this.desc();
    }

}
