package com.qzing.bsp.osc.service;

import com.qzing.bsp.osc.annotation.ModuleInfo;
import com.qzing.bsp.osc.dao.RiskSuspicionRecordDtlDao;
import com.qzing.bsp.osc.entity.RiskSuspicionRecord;
import com.qzing.ieep.coding.template.service.ServiceImplTemplate;
import com.qzing.ieep.coding.template.service.SimpleServiceImplTemplate;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 风险疑点记录服务实现类
 */
@ModuleInfo(moduleCode = "FXYD")
public class StdRiskSuspicionRecordServiceImpl extends SimpleServiceImplTemplate<RiskSuspicionRecord, Long> implements StdRiskSuspicionRecordService {

    @Autowired
    protected RiskSuspicionRecordDtlDao riskSuspicionRecordDtlDao;


}
