package com.qzing.bsp.osc.util;

import com.qzing.bsp.osc.annotation.OSCFieldRequired;
import com.qzing.ieep.context.CurrentContextHelper;
import com.qzing.ieep.core.exception.BizException;
import com.qzing.ieep.data.common.FieldMetadata;
import com.qzing.ieep.i18n.util.I18NUtils;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.ManyToOne;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 安全校验工具类
 * 功能特点：
 * 1. 支持多级嵌套校验（主单+明细）
 * 2. 自动检测并处理循环引用
 * 3. 支持JPA实体双向关联
 * 4. 提供清晰的错误路径信息
 * 5. 线程安全实现
 */
public class OSCFieldRequiredUtils {

    /**
     * 执行校验并返回所有错误信息
     * @param rootEntity 要校验的根实体对象
     * @return 错误信息字符串（多个错误用换行分隔），null表示校验通过
     */
    public static String validate(Object rootEntity) {
        ValidationContext context = new ValidationContext();
        doValidate(rootEntity, null, context);
        return context.getErrorString();
    }

    /**
     * 执行校验并在失败时抛出异常
     * @param rootEntity 要校验的根实体对象
     * @throws ValidationException 当校验失败时抛出，包含所有错误信息
     */
    public static void validateAndThrow(Object rootEntity) {
        String errors = validate(rootEntity);
        if (errors != null) {
            BizException.throwEx(errors);
        }
    }

    /**
     * 实际校验逻辑
     * @param currentObj
     * @param currentPath
     * @param context
     */
    private static void doValidate(Object currentObj, String currentPath, ValidationContext context) {
        if (currentObj == null || context.isAlreadyProcessed(currentObj)) {
            return;
        }
        //字段不能为空结尾提示
        String notBlankMessage = I18NUtils.getText(CurrentContextHelper.getLocale(), "vendor.canNotBeEmpty");
        if(StringUtils.isNotBlank(notBlankMessage)) {
            context.setNotBlankMessage(notBlankMessage);
        }
        context.markAsProcessed(currentObj);

        // 获取类继承链中的所有字段（包括父类）
        List<Field> allFields = getAllFields(currentObj.getClass());

        for (Field field : allFields) {
            try {
                field.setAccessible(true);
                Object fieldValue = field.get(currentObj);
                String fieldName = getFieldDisplayName(field);
                String fieldPath = buildFieldPath(currentPath, fieldName);

                // 校验字段基本规则
                validateFieldValue(field, fieldValue, fieldPath, context);

                // 处理嵌套结构
                processNestedStructure(field, fieldValue, fieldPath, context);

            } catch (IllegalAccessException e) {
                context.addError("无法访问字段: " + field.getName());
            }
        }
    }

    /**
     * 获取类继承链中的所有字段（包括父类）
     * @param clazz
     * @return
     */
    private static List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        Class<?> currentClass = clazz;

        // 遍历整个继承链
        while (currentClass != null && currentClass != Object.class) {
            Collections.addAll(fields, currentClass.getDeclaredFields());
            currentClass = currentClass.getSuperclass();
        }
        return fields;
    }

    /**
     * 校验字段值是否符合规则
     * @param field
     * @param value
     * @param fieldPath
     * @param context
     */
    private static void validateFieldValue(Field field, Object value, String fieldPath, ValidationContext context) {
        OSCFieldRequired required = field.getAnnotation(OSCFieldRequired.class);
        if (required == null) {
            return;
        }

        if (value == null) {
            context.addError(fieldPath + context.notBlankMessage);
            return;
        }

        if (value instanceof String && StringUtils.isBlank((String) value)) {
            context.addError(fieldPath + context.notBlankMessage);
            return;
        }

        if (value instanceof Collection && CollectionUtils.isEmpty((Collection<?>) value)) {
            context.addError(fieldPath + context.notBlankMessage);
        }
    }

    // 处理嵌套结构（集合/数组/对象）
    private static void processNestedStructure(Field field, Object value, String parentPath, ValidationContext context) {
        if (value == null) {
            return;
        }

        // 跳过对父实体的反向引用
        if (isParentReference(field)) {
            return;
        }

        if (value instanceof Collection) {
            processCollection((Collection<?>) value, parentPath, context);
        }
        else if (value.getClass().isArray()) {
            processArray((Object[]) value, parentPath, context);
        }
        else if (!isJavaLangClass(value.getClass())) {
            doValidate(value, parentPath, context);
        }
    }

    // 处理集合类型的字段
    private static void processCollection(Collection<?> collection, String parentPath, ValidationContext context) {
        if (collection == null || collection.isEmpty()) {
            return;
        }

        int index = 0;
        for (Object item : collection) {
            if (item != null) {
                String itemPath = parentPath + "[" + (index+1) + "]";
                doValidate(item, itemPath, context);
                index++;
            }
        }
    }

    /**
     * 处理数组类型的字段
     * @param array
     * @param parentPath
     * @param context
     */
    private static void processArray(Object[] array, String parentPath, ValidationContext context) {
        if (array == null || array.length == 0) {
            return;
        }

        int index = 0;
        for (Object item : array) {
            if (item != null) {
                String itemPath = parentPath + "[" + index + "]";
                doValidate(item, itemPath, context);
                index++;
            }
        }
    }

    /**
     * 判断是否是关联父对象的字段
     * @param field
     * @return
     */
    private static boolean isParentReference(Field field) {
        return field.getAnnotation(ManyToOne.class) != null;
    }

    /**
     * 构建字段路径
     * @param parentPath
     * @param fieldName
     * @return
     */
    private static String buildFieldPath(String parentPath, String fieldName) {
        return parentPath == null ? fieldName : parentPath + "." + fieldName;
    }

    /**
     * 获取字段显示名称
     * @param field
     * @return
     */
    private static String getFieldDisplayName(Field field) {
        FieldMetadata metadata = field.getAnnotation(FieldMetadata.class);
        if (metadata != null) {
            if (StringUtils.isNotBlank(metadata.resourceKey())) {
                return getI18nText(metadata.resourceKey());
            }
            if (StringUtils.isNotBlank(metadata.name())) {
                return metadata.name();
            }
        } else {
            ApiModelProperty apiModelProperty = field.getAnnotation(ApiModelProperty.class);
            if (apiModelProperty != null) {
                if (StringUtils.isNotBlank(apiModelProperty.value())) {
                    return apiModelProperty.value();
                }
            }
        }
        return field.getName();
    }

    /**
     * 判断是否是Java原生类
     * @param clazz
     * @return
     */
    private static boolean isJavaLangClass(Class<?> clazz) {
        return clazz != null && clazz.getClassLoader() == null;
    }

    /**
     * 获取国际化文本
     * @param resourceKey
     * @return
     */
    private static String getI18nText(String resourceKey) {
        // 实际项目中应该从资源文件获取
        String text = I18NUtils.getText(CurrentContextHelper.getLocale(), resourceKey);
        return StringUtils.isNotBlank(text)?text:resourceKey;
    }

    /**
     * 校验上下文（线程安全）
     */
    private static class ValidationContext {
        private String notBlankMessage = "不能为空";
        private final Set<Object> processedObjects = Collections.newSetFromMap(new ConcurrentHashMap<>());
        private final List<String> errors = Collections.synchronizedList(new ArrayList<>());

        boolean isAlreadyProcessed(Object obj) {
            return processedObjects.contains(obj);
        }

        void markAsProcessed(Object obj) {
            processedObjects.add(obj);
        }

        void setNotBlankMessage(String message) {
            notBlankMessage = message;
        }

        void addError(String error) {
            errors.add(error);
        }

        String getErrorString() {
            return errors.isEmpty() ? null : String.join(";", errors);
        }
    }

    /**
     * 校验异常
     */
    public static class ValidationException extends Exception {
        public ValidationException(String message) {
            super(message);
        }
    }
}