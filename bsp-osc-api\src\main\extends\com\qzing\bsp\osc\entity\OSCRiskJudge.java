package com.qzing.bsp.osc.entity;

import com.qzing.ieep.data.common.DataPermission;
import com.qzing.ieep.datadriven.DataDrivenEntityListener;
import com.qzing.ieep.logging.listener.AuditListener;

import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;

/**
 * 风险研判管理
 */
@Entity
@Table(name = "d_osc_riskjudge")
@EntityListeners({AuditListener.class, DataDrivenEntityListener.class})
@DataPermission(billTypeCode = "RJS")
public class OSCRiskJudge extends StdOSCRiskJudge {

	private static final long serialVersionUID = 1412144913886840103L;

}
