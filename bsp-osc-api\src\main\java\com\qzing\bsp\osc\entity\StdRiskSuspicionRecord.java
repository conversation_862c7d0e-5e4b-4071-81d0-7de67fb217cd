package com.qzing.bsp.osc.entity;


import com.qzing.ieep.data.annotation.BizNumber;
import com.qzing.ieep.data.common.FieldMetadata;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * 风险疑点记录主单实体类-基类
 */
@Data
@MappedSuperclass
public class StdRiskSuspicionRecord extends OSCEntity<Long> {

    private static final long serialVersionUID = 2526335682158437861L;

    @Id
    @GenericGenerator(strategy = "com.qzing.ieep.data.jpa.support.SnowFlakeIdGenerator", name = "SnowFlakeIdGenerator")
    @GeneratedValue(generator = "SnowFlakeIdGenerator")
    @Column(name = "riskSuspicionRecordId")
    @FieldMetadata(name = "记录ID", resourceKey = "label.recordId", skipHistoryComparison = true)
    protected Long riskSuspicionRecordId;

    @BizNumber
    @Column(name = "riskSuspicionRecordNo")
    @FieldMetadata(name = "单号", resourceKey = "label.riskSuspicionRecordNo")
    protected String riskSuspicionRecordNo;
    @Column(name = "secretLevel")
    @FieldMetadata(name = "密级", resourceKey = "label.secretLevel")
    protected String secretLevel;
    @Column(name = "sceneCode")
    @FieldMetadata(name = "场景编码", resourceKey = "label.sceneCode")
    protected String sceneCode;

    @Column(name = "sceneName")
    @FieldMetadata(name = "场景名称", resourceKey = "label.sceneName")
    protected String sceneName;

    @Column(name = "status")
    @FieldMetadata(name = "状态", resourceKey = "label.status")
    protected String status;

    @Column(name = "unitCode")
    @FieldMetadata(name = "单位编码", resourceKey = "label.unitCode")
    protected String unitCode;

    @Column(name = "unitName")
    @FieldMetadata(name = "单位名称", resourceKey = "label.unitName")
    protected String unitName;

    @Column(name = "bizType")
    @FieldMetadata(name = "业务类型", resourceKey = "label.bizType")
    protected String bizType;

    @Column(name = "supervisionType")
    @FieldMetadata(name = "监管类型", resourceKey = "label.supervisionType")
    protected String supervisionType;

    @Column(name = "dtlCount")
    @FieldMetadata(name = "统计数(疑点明细数量)", resourceKey = "label.dtlCount")
    protected Integer dtlCount;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "runTime")
    @FieldMetadata(name = "运行时间", resourceKey = "label.runTime")
    protected Calendar runTime;

    @Column(name = "source")
    @FieldMetadata(name = "来源", resourceKey = "label.source")
    protected String source;

    @Column(name = "assignedPersonCode")
    @FieldMetadata(name = "分配人员编码", resourceKey = "label.assignedPersonCode")
    protected String assignedPersonCode;

    @Column(name = "assignedPersonName")
    @FieldMetadata(name = "分配人员姓名", resourceKey = "label.assignedPersonName")
    protected String assignedPersonName;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "riskSuspicionRecord", fetch = FetchType.LAZY, orphanRemoval = true)
    @FieldMetadata(name = "疑点明细", resourceKey = "label.suspicionDetails")
    protected List<RiskSuspicionRecordDtl> suspicionDetails = new ArrayList<RiskSuspicionRecordDtl>();
}
