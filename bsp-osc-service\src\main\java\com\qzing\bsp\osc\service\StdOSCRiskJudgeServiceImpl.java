package com.qzing.bsp.osc.service;

import com.qzing.bsp.osc.annotation.ModuleInfo;
import com.qzing.bsp.osc.constant.OSCBillType;
import com.qzing.bsp.osc.dao.OSCRiskJudgeDtlDao;
import com.qzing.bsp.osc.entity.OSCRiskJudge;
import com.qzing.bsp.osc.entity.OSCRiskJudgeDtl;
import com.qzing.ieep.coding.template.service.ServiceImplTemplate;
import com.qzing.ieep.data.common.Page;
import com.qzing.ieep.data.jpa.entity.BaseEntity;
import com.qzing.ieep.data.jpa.entity.BizEntity;
import com.qzing.ieep.data.jpa.entity.IFileInfo;
import com.qzing.ieep.data.jpa.support.JpaSearchRequest;
import com.qzing.ieep.event.biz.UpdateAsyncEvent;
import com.qzing.ieep.event.biz.UpdateEvent;
import com.qzing.ieep.file.api.util.FileInfoUtils;
import com.qzing.ieep.util.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 风险评判
 */
@ModuleInfo(moduleCode = "RJS")
public class StdOSCRiskJudgeServiceImpl extends ServiceImplTemplate<OSCRiskJudge, Long> implements StdOSCRiskJudgeService {

    @Autowired
    protected OSCRiskJudgeDtlDao riskJudgeDtlDao;
    /**
     * 获取详情
     * @param riskJudgeNo
     * @param extraInfo
     * @return
     */
    public OSCRiskJudge get(String riskJudgeNo, Map<String, Object> extraInfo) {
        OSCRiskJudge response = new OSCRiskJudge();
        Map<String, Object> searchMap = new HashMap<>();
        searchMap.put("EQ_riskJudgeNo", riskJudgeNo);
        OSCRiskJudge entity = findOne(searchMap);
        if (null != entity) {
            BeanUtils.copyProperties(entity, response, "riskJudgeDtls");
        }
        return response;
    }

    /**
     * 明细
     * @param riskJudgeNo
     * @param extraInfo
     * @return
     */
    public List<OSCRiskJudgeDtl> getDetail(String riskJudgeNo, Map<String, Object> extraInfo) {
        List<OSCRiskJudgeDtl> oscRiskJudgeDtlResponseList = new ArrayList<>();
        Map<String, Object> searchMap = new HashMap<>();
        searchMap.put("EQ_riskJudge_riskJudgeNo", riskJudgeNo);
        List<OSCRiskJudgeDtl> detailList = riskJudgeDtlDao.findAll(searchMap);
        if (null != detailList) {
            for (OSCRiskJudgeDtl judgeDtl: detailList) {
                OSCRiskJudgeDtl responseDtl = new OSCRiskJudgeDtl();
                BeanUtils.copyProperties(judgeDtl, responseDtl, "riskJudge");
                oscRiskJudgeDtlResponseList.add(responseDtl);
            }
        }
        return oscRiskJudgeDtlResponseList;
    }

    /**
     * 设置一对多
     * @param entity
     */
    @Override
    protected void setEntityRelation(OSCRiskJudge entity) {
        if (null != entity.getRiskJudgeDtls()) {
            for (OSCRiskJudgeDtl item: entity.getRiskJudgeDtls()) {
                item.setRiskJudge(entity);
            }
        }
    }

    @Override
    protected void beforeSave(OSCRiskJudge entity, Map<String, Object> extraMap) {
        String riskJudgeNo = entity.getRiskJudgeNo();
        if (StringUtils.isBlank(riskJudgeNo)) {
            riskJudgeNo = billSetClient.createNextRunningNum(OSCBillType.RJS);
            entity.setRiskJudgeNo(riskJudgeNo);
        }
    }

    @Override
    protected OSCRiskJudge doSave(OSCRiskJudge entity, Map<String, Object> extraMap) {
        OSCRiskJudge bizEntity = save(entity);
        return bizEntity;
    }

    /**
     * 新增保存
     * @param entity
     * @param extraMap
     * @return
     */
    public OSCRiskJudge save(OSCRiskJudge entity, Map<String, Object> extraMap) {
        validate(entity, extraMap);
        setEntityRelation(entity);
        entity = save(entity);
        return entity;
    }

    /**
     * 更新
     * @param entity
     * @param extraMap
     * @return
     */
    public OSCRiskJudge update(OSCRiskJudge entity, Map<String, Object> extraMap) {
        Long serializable = this.dao.getEntityId(entity);
        OSCRiskJudge bizEntity = findById(serializable);
        extraMap.put("entity", bizEntity);
        setEntityRelation(entity);
        validate(entity, extraMap);

        validateDataChanged(entity, bizEntity);

        beforeUpdate(entity, extraMap);

        OSCRiskJudge newEntity = doUpdate(entity, extraMap);

        return entity;
    }

    /**
     * 列表
     * @param request
     * @param <S>
     * @return
     */
    public <S> Page<S> list(JpaSearchRequest request) {
        if (StringUtils.isBlank(request.getSorted())) {
            request.setSorted("riskJudgeId desc");
        }

        Class<S> voClass = getVoClass();

        if (voClass != null) {
            request.setResultType(voClass);
        }

        beforeList(request);

        Page<S> page = searchPage(request);

        if (voClass != null && IFileInfo.class.isAssignableFrom(voClass)) {
            FileInfoUtils.buildFileInfo((List<? extends IFileInfo>) page.getRecords());
        }

        page = afterList(page);
        return page;
    }

    /**
     * 添加日志
     * @param actionId
     * @param ei
     * @param billId
     */
    /*public void addLog(String actionId, ExtraInfo ei, Long billId){
        String requestId = ei.getRequestId();
        OperateLog operateLog = OperateLogUtils.OperateLogBuilder.builder()
                .action(actionId)
                .moduleCode(getModuleCode())
                .bizkey(billId.toString())
                .terminal(CurrentContextHelper.getTerminal())
                .operatorHost(CurrentContextHelper.getUserRealIp())
                .operatorId(CurrentContextHelper.getUserId())
                .operatorName(CurrentContextHelper.getUserName())
                .requestId(requestId)
                .requestTime(CurrentContextHelper.getRequestTime())
                .responseTime(Calendar.getInstance())
                .businessNo(billId.toString())
                .module(getModuleCode())
                .message(ei.getString(""))
                .content(ei.getString(""))
                .contentParamJson(ei.getReplace()).build();

        OperateLogUtils.log(operateLog);
    }*/

}
