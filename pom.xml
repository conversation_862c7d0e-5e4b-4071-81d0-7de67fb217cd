<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qzing.ieep2</groupId>
        <artifactId>ieep2-dependencies</artifactId>
        <version>3.2.1.casic-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <groupId>com.qzing.srm</groupId>
    <artifactId>bsp-osc-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <scm>
        <url>http://10.10.20.35/microservice//pec/pec</url>
        <connection>scm:git:http://10.10.20.35/microservice/osc/osc</connection>
        <developerConnection>scm:git:http://10.10.20.35/microservice/osc/osc</developerConnection>
    </scm>
    <properties>
        <maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
        <poi.version>4.1.0</poi.version>
        <qzing.designer.version>2.3.16-SNAPSHOT</qzing.designer.version>
        <ieep2.coding-template.version>3.2.0-SNAPSHOT</ieep2.coding-template.version>
    </properties>
    <distributionManagement>
        <repository>
            <id>qzingtech-releases</id>
            <name>Huiju Nexus Release Repository</name>
            <url>http://10.10.20.22:8081/repository/maven-releases/</url>
            <layout>default</layout>
        </repository>
        <snapshotRepository>
            <id>qzingtech-snapshots</id>
            <name>Huiju Nexus Snapshot Repository</name>
            <url>http://10.10.20.22:8081/repository/maven-snapshots/</url>
            <layout>default</layout>
        </snapshotRepository>
    </distributionManagement>

    <dependencies>

        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-admin-client</artifactId>
        </dependency>
        <!-- 程序监控器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!-- nacos注册中心客户端 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!-- ribbon + hystrix -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <!-- springboot -->
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
        </dependency>
        <!-- 其他 -->
        <dependency>
            <groupId>com.beust</groupId>
            <artifactId>jcommander</artifactId>
            <version>1.7</version>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
        </dependency>
        <dependency>
            <groupId>net.sourceforge.barbecue</groupId>
            <artifactId>barbecue</artifactId>
        </dependency>
        <dependency>
            <groupId>net.sf.barcode4j</groupId>
            <artifactId>barcode4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-bridge</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-anim</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-awt-util</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-css</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-dom</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-ext</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-gvt</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-parser</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-script</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-svg-dom</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-svggen</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-util</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-xml</artifactId>
        </dependency>
        <dependency>
            <groupId>apache-xerces</groupId>
            <artifactId>xercesImpl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.xhtmlrenderer</groupId>
            <artifactId>flying-saucer-pdf</artifactId>
        </dependency>
        <!-- javaee-api -->
        <dependency>
            <groupId>javax</groupId>
            <artifactId>javaee-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>

            <!-- 前端渲染引擎 -->
            <dependency>
                <groupId>com.qzing.ui</groupId>
                <artifactId>designer-runtime-all</artifactId>
                <version>${qzing.designer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-logging-api</artifactId>
                <version>${ieep.apps.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-starter-consul-discovery</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-starter-consul</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.qzing.ui</groupId>
                <artifactId>designer-runtime-all</artifactId>
                <version>${qzing.designer.version}</version>
            </dependency>

            <!-- yaml -->
            <dependency>
                <groupId>it.ozimov</groupId>
                <artifactId>yaml-properties-maven-plugin</artifactId>
                <version>1.1.3</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>1.4</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.7</version>
            </dependency>
            <!-- ieep2 -->
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-graphql</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-mq-api</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-event-driven</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-cache-api</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-cache-redis</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-config</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-core</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-mail</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-wechat</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-data</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-excel</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-groovy</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-i18n</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-fs</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-json</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-log</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-license</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-mvc</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-xml</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-message-driven</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-admin-api</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-iam-api</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-platform-api</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-notify-api</artifactId>
                <version>${ieep.starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-web</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-scheduler-api</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-logging-api</artifactId>
                <version>${ieep.starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-starter-consul-discovery</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-starter-consul</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-export</artifactId>
                <version>${ieep.starter.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>poi</artifactId>
                        <groupId>org.apache.poi</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-starter-data-driven</artifactId>
                <version>${ieep.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-coding-template</artifactId>
                <version>${ieep2.coding-template.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>build-helper-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>add-source</id>
						<phase>generate-sources</phase>
						<goals>
							<goal>add-source</goal>
						</goals>
						<configuration>
							<sources>
								<source>src/main/extends</source>
								<source>src/main/api</source>
							</sources>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-jar-plugin</artifactId>
				<executions>
					<execution>
						<id>jar</id>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<classifier>std</classifier>
							<includes>
								<include>**/controller/Std**</include>
								<include>**/dao/**</include>
								<include>**/entity/Std*</include>
								<include>**/schedule/Std*</include>
								<include>**/service/third/external/Std*</include>
								<include>**/service/third/internal/Std*</include>
								<include>**/service/Std*</include>
							</includes>
						</configuration>
					</execution>
					<execution>
						<id>stdentity</id>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<classifier>stdentity</classifier>
							<includes>
								<include>**/entity/Std*</include>
							</includes>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.codehaus.mojo</groupId>
					<artifactId>build-helper-maven-plugin</artifactId>
					<version>1.4</version>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>

    <modules>
		<module>bsp-osc-api</module>
		<module>bsp-osc-service</module>
        <module>bsp-osc-service-thin</module>
    </modules>
</project>
