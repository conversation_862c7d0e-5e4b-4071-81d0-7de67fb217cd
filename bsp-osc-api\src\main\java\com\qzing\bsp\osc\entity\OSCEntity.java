package com.qzing.bsp.osc.entity;

import com.qzing.ieep.context.CurrentContextHelper;
import com.qzing.ieep.context.UserAuthInfo;
import com.qzing.ieep.data.common.FieldMetadata;
import com.qzing.ieep.data.jpa.entity.BaseEntity;
import com.qzing.ieep.data.jpa.entity.BizEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Calendar;


/**
 * OSC 实体服务，用于取OSC各个微服务公共属性和设置
 *
 */
@Data
@MappedSuperclass
public class OSCEntity<ID extends Serializable> extends BizEntity<ID> {

    private static final long serialVersionUID = 175612223653398443L;

    @Column(name = "secretLevel")
    @FieldMetadata(name = "密级", resourceKey = "label.secretLevel")
    protected String secretLevel;


    @Column(
            name = "remark"
    )
    @FieldMetadata(
            name = "备注",
            resourceKey = "label.remark"
    )
    protected String remark;
    @FieldMetadata(
            name = "附件组ID",
            resourceKey = "label.uploadFileGroupId"
    )
    @Column(
            name = "fileGroupId"
    )
    protected Long fileGroupId;
    @Column(
            updatable = false
    )
    @FieldMetadata(
            name = "创建人ID",
            resourceKey = "label.createUserId"
    )
    @ApiModelProperty("创建人ID")
    protected Long createUserId;
    @Column(
            updatable = false
    )
    @FieldMetadata(
            name = "创建人名称",
            resourceKey = "label.createUserName"
    )
    @ApiModelProperty("创建人名称")
    protected String createUserName;
    @Column(
            updatable = false
    )
    @Temporal(TemporalType.TIMESTAMP)
    @FieldMetadata(
            name = "创建时间",
            resourceKey = "label.createTime"
    )
    @ApiModelProperty(
            value = "创建时间",
            dataType = "string",
            example = "2022-01-01 23:59:59"
    )
    protected Calendar createTime;
    @FieldMetadata(
            name = "修改人ID",
            resourceKey = "label.modifyUserId"
    )
    @ApiModelProperty("修改人ID")
    protected Long modifyUserId;
    @FieldMetadata(
            name = "修改人名称",
            resourceKey = "label.modifyUserName"
    )
    @ApiModelProperty("修改人名称")
    protected String modifyUserName;
    @Temporal(TemporalType.TIMESTAMP)
    @FieldMetadata(
            name = "修改时间",
            resourceKey = "label.modifyTime"
    )
    @ApiModelProperty(
            value = "修改时间",
            dataType = "string",
            example = "2022-01-01 23:59:59"
    )
    protected Calendar modifyTime;
    @PrePersist
    public void onPrePersist() {
        UserAuthInfo info = CurrentContextHelper.getUserAuthInfo();
        if (info != null) {
            this.setClientCode(info.getClientCode());
            if (this.getCreateUserId() == null) {
                this.setCreateUserId(info.getUserId());
            }

            if (this.getCreateUserName() == null) {
                this.setCreateUserName(info.getUserName());
            }

            if (this.getModifyUserId() == null) {
                this.setModifyUserId(info.getUserId());
            }
            if (this.getModifyUserName() == null) {
                this.setModifyUserName(info.getUserName());
            }
        }
        if (this.getCreateTime() == null) {
            this.setCreateTime(Calendar.getInstance());
        }
        this.setModifyTime(Calendar.getInstance());
    }

    @PreUpdate
    public void onPreUpdate() {
        UserAuthInfo info = CurrentContextHelper.getUserAuthInfo();
        if (info != null) {
            this.setClientCode(info.getClientCode());
            if (this.getModifyUserId() == null) {
                this.setModifyUserId(info.getUserId());
            }
            if (this.getModifyUserName() == null) {
                this.setModifyUserName(info.getUserName());
            }
        }
        this.setModifyTime(Calendar.getInstance());
    }

}
