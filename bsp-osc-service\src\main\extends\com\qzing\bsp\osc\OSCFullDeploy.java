package com.qzing.bsp.osc;

import com.qzing.framework.core.repository.DefaultWiselyRepositoryImpl;
import com.qzing.framework.query.advance.jpa.JpaRepositoryFactoryBeanExt;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * 协同采购服务
 * 
 * <AUTHOR>
 *
 */
@EnableDiscoveryClient
@SpringBootApplication(
		exclude = {FlywayAutoConfiguration.class}
)
@ComponentScan(
		basePackages = {"com.qzing"},
		excludeFilters = {@ComponentScan.Filter(
				pattern = {"com.qzing.ieep.config.service.*", "com.qzing.ieep.file.api.logic.*", "com.qzing.ieep.file.api.support.*", "com.qzing.cmn.config.CMNSwaggerConfig", "com.qzing.lmc.config.LMCSwaggerConfig", "com.qzing.wms.config.SwaggerConfig", "com.qzing.cmn.entity.SrmState"},
				type = FilterType.REGEX
		)}
)
@EnableFeignClients(
		basePackages = {"com.qzing.ieep.iam.api", "com.qzing.ieep.logging.api", "com.qzing.ieep.uaa.api", "com.qzing.ieep.platform.api", "com.qzing.bpm.api", "com.qzing.core.sys.api", "com.qzing.ieep.notify.api", "com.qzing.ieep.event.client", "com.qzing.bsp", "com.qzing.md", "com.qzing.ieep.api.feignclient", "com.qzing.ieep.workbench.client", "com.qzing.ieep.mail.api", "com.qzing.ime", "com.qzing.ieep.notify.client", "com.qzing.ieep.file.api", "com.qzing.dc", "com.qzing.ieep.integration.feignclient", "com.qzing.cmn", "com.qzing.orchestration.client", "com.qzing.ieep.rule.centre.api", "com.qzing.ieep.portal.api", "com.qzing.wms.im.client", "com.qzing.ieep2.thirdapps", "com.qzing.cmc.client.api"}
)
@EntityScan(
		basePackages = {"com.qzing"}
)
@EnableJpaRepositories(
		repositoryBaseClass = DefaultWiselyRepositoryImpl.class,
		repositoryFactoryBeanClass = JpaRepositoryFactoryBeanExt.class,
		basePackages = {"com.qzing"}
)
public class OSCFullDeploy {

	public static void main(String[] args) {
		SpringApplication.run(OSCFullDeploy.class, args);
	}

}
