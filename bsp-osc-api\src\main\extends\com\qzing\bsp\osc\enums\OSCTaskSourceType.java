package com.qzing.bsp.osc.enums;

/**
 * 任务来源类型
 */
public enum  OSCTaskSourceType {

    SYSTEM("系统"),
    MANUAL("手动"),
    IMPORT("导入"),
    INTERFACE("接口");

    private String desc;

    private OSCTaskSourceType(String desc) {
        this.desc = desc;
    }

    public String desc() {
        return this.desc;
    }

    public Integer value() {
        return this.ordinal();
    }

    public String toString() {
        return String.valueOf(this.ordinal());
    }

    public int getIndex() {
        return this.ordinal();
    }

    public String getName() {
        return this.name();
    }

    public String getStateDesc() {
        return this.desc();
    }
}
