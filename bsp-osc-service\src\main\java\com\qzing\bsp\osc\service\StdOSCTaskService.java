package com.qzing.bsp.osc.service;

import com.qzing.bsp.osc.entity.OSCTask;
import com.qzing.bsp.osc.entity.OSCTaskProcessRecord;
import com.qzing.ieep.coding.template.service.ServiceTemplate;
import com.qzing.ieep.data.common.RestResponse;
import com.qzing.ieep.data.jpa.support.JpaSearchRequest;

import java.util.List;


/**
 * 任务管理
 */
public interface StdOSCTaskService extends ServiceTemplate<OSCTask, Long> {

    /**
     * 获取详情
     * @param request
     * @return
     */
    OSCTask get(OSCTask request);

    /**
     * 执行记录列表
     * @param request
     * @return
     */
    List<OSCTaskProcessRecord> findProcessRecordList(JpaSearchRequest request);

    /**
     * 派单	指派任务负责人，并且发送消息通知
     * @param request
     * @return
     */
    RestResponse dispatch(OSCTask request);

    /**
     * 任务完成	任务负责人支持提交完成任务，上传相关附件，通知创建人
     * @return
     */
    RestResponse finish(OSCTask request);

    /**
     * 关闭	创建人进行验收，没有问题，关闭该任务
     * @return
     */
    RestResponse close(OSCTask request);

    /**
     * 激活	创建人进行验收，有疑问激活任务
     * @param request
     * @return
     */
    RestResponse activation(OSCTask request);

    /**
     * 重新派单	因为任务负责人无法执行，支持风控人重新派单
     * @param request
     * @return
     */
    RestResponse reDispatch(OSCTask request);

    /**
     * 催办	支持风控人对任务负责人进行催办提醒
     * @param request
     * @return
     */
    RestResponse urging(OSCTask request);

    /**
     * 任务预警	任务快到期，还没有完成，支持提前预警
     * @param request
     * @return
     */
    RestResponse warning(OSCTask request);

    /**
     * 挂起	支持任务挂起，后续在激活执行
     * @param request
     * @return
     */
    RestResponse pending(OSCTask request);

//    编辑	当任务无法按时完成时，也支持调整任务计划

//    取消	任务因为种种原因无法执行，支持进行取消

    //删除	还未分派的任务支持手工删除


}
