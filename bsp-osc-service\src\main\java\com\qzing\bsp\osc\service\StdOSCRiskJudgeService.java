package com.qzing.bsp.osc.service;

import com.qzing.bsp.osc.entity.OSCRiskJudge;
import com.qzing.bsp.osc.entity.OSCRiskJudgeDtl;
import com.qzing.bsp.osc.entity.OSCTask;
import com.qzing.ieep.coding.template.service.ServiceTemplate;

import java.util.List;
import java.util.Map;


/**
 * 风险评判
 */
public interface StdOSCRiskJudgeService extends ServiceTemplate<OSCRiskJudge, Long> {

    /**
     * 获取详情
     * @param riskJudgeNo
     * @param extraInfo
     * @return
     */
    OSCRiskJudge get(String riskJudgeNo, Map<String, Object> extraInfo);

    /**
     * 明细
     * @param riskJudgeNo
     * @param extraInfo
     * @return
     */
    List<OSCRiskJudgeDtl> getDetail(String riskJudgeNo, Map<String, Object> extraInfo);

}
