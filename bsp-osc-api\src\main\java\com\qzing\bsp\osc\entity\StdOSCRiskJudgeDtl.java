package com.qzing.bsp.osc.entity;

import com.qzing.ieep.data.common.FieldMetadata;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @description [风险研判-明细]
 * @date 2025/7/25
*/
@Data
@MappedSuperclass
public class StdOSCRiskJudgeDtl extends OSCEntity<Long> {

    private static final long serialVersionUID = 3414522766721489874L;

    @Id
    @GenericGenerator(strategy = "com.qzing.ieep.data.jpa.support.SnowFlakeIdGenerator", name = "SnowFlakeIdGenerator")
    @GeneratedValue(generator = "SnowFlakeIdGenerator")
    @Column(name = "riskJudgeDtlId")
    @FieldMetadata(name = "主键ID", resourceKey = "label.taskId", skipHistoryComparison = true)
    protected Long riskJudgeDtlId;

    @FieldMetadata(name = "主单", skipHistoryComparison = true)
    @ManyToOne
    @JoinColumn(name = "riskJudgeId", referencedColumnName = "riskJudgeId")
    protected OSCRiskJudge riskJudge;

    @Column(name = "rowNo")
    @FieldMetadata(name = "行号", resourceKey = "OSCRISK.label.rowNo")
    protected String rowNo;

    @Column(name = "suspiciousDesc")
    @FieldMetadata(name = "疑点描述", resourceKey = "OSCRISK.label.suspiciousDesc")
    protected String suspiciousDesc;

    @Column(name = "riskLogDetail")
    @FieldMetadata(name = "风险登记记录明细", resourceKey = "OSCRISK.label.riskLogDetail")
    protected String riskLogDetail;

    @Column(name = "tagStatus")
    @FieldMetadata(name = "标注状态", resourceKey = "OSCRISK.label.tagStatus")
    protected String tagStatus;

    @Column(name = "taskStatus")
    @FieldMetadata(name = "任务执行状态", resourceKey = "OSCRISK.label.taskStatus")
    protected String taskStatus;

    @Column(name = "riskLevel")
    @FieldMetadata(name = "风险等级", resourceKey = "OSCRISK.label.riskLevel")
    protected String riskLevel;

    @Column(name = "judgeDesc")
    @FieldMetadata(name = "研判说明", resourceKey = "OSCRISK.label.desc")
    protected String judgeDesc;

    @Column(name = "closeReason")
    @FieldMetadata(name = "关闭原因", resourceKey = "OSCRISK.label.closeReason")
    protected String closeReason;


}
