<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qzing.srm</groupId>
        <artifactId>bsp-osc-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <artifactId>bsp-osc-service</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.qzing.ui</groupId>
            <artifactId>designer-runtime-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.srm</groupId>
            <artifactId>bsp-osc-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity</artifactId>
            <version>1.7</version>
        </dependency>

        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-data-driven</artifactId>
        </dependency>
        <!-- ieep2-jar -->
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-admin-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-iam-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-organization-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-platform-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-apimgmt-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-notify-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-scheduler-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-logging-api</artifactId>
        </dependency>
        <!-- ieep2-starter -->
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-cache-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-cache-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-web</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-data</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-excel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-groovy</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-i18n</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-json</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-license</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-web</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-mvc</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-web</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-xml</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-event-driven</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-mq-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-graphql</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-export</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-orchestration-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-coding-template</artifactId>
        </dependency>
        <!-- JAX-WS API（包含 javax.jws 包） -->
        <dependency>
            <groupId>javax.xml.ws</groupId>
            <artifactId>jaxws-api</artifactId>
            <version>2.3.1</version>
        </dependency>
        <!-- 如果使用 Spring Boot，可能需要额外添加 JAX-WS 运行时实现 -->
        <dependency>
            <groupId>com.sun.xml.ws</groupId>
            <artifactId>jaxws-rt</artifactId>
            <version>2.3.3</version>
            <scope>runtime</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                    <classifier>exec</classifier>
                    <mainClass>com.qzing.bsp.osc.OSCFullDeploy</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>